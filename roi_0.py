# 导入必要的库和模块
import cv2
import numpy as np

# 全局变量
roi = None
drawing = False
ix, iy = -1, -1
fx, fy = -1, -1

#实现鼠标与cv2窗口的交互, 绘制矩形框
def draw_rectangle(event, x, y, flags, param):
    global ix, iy, fx, fy, drawing, roi

    if event == cv2.EVENT_LBUTTONDOWN:
        drawing = True
        ix, iy = x, y

    elif event == cv2.EVENT_MOUSEMOVE:
        if drawing:
            fx, fy = x, y

    elif event == cv2.EVENT_LBUTTONUP:
        drawing = False
        fx, fy = x, y
        roi = (min(ix, fx), min(iy, fy), abs(fx - ix), abs(fy - iy))

# 处理摄像头输入，并显示选择的ROI区域和正在绘制的矩形框
def process_camera():
    global roi
    print("正在初始化摄像头...")
    cap = cv2.VideoCapture(0)

    # 检查摄像头是否成功打开
    if not cap.isOpened():
        print("错误：无法打开摄像头！")
        print("请检查：")
        print("1. 摄像头是否连接正常")
        print("2. 摄像头是否被其他程序占用")
        print("3. 系统摄像头权限设置")
        return

    # 设置摄像头参数
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

    print("摄像头初始化成功！")
    print("操作说明：")
    print("- 鼠标拖拽选择ROI区域")
    print("- 按 'r' 键重置ROI")
    print("- 按 'q' 键退出程序")

    cv2.namedWindow('Select ROI')
    cv2.setMouseCallback('Select ROI', draw_rectangle)

    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            print("错误：无法读取摄像头帧数据")
            break

        frame_count += 1
        if frame_count == 1:
            print(f"摄像头分辨率: {frame.shape[1]}x{frame.shape[0]}")

        clone = frame.copy()
        temp = frame.copy()

        # 显示已选择的ROI区域
        if roi is not None:
            x, y, w, h = roi
            cv2.rectangle(temp, (x, y), (x + w, y + h), (0, 255, 0), 2)
            text = f"ROI: ({x}, {y}, {w}, {h})"
            cv2.putText(temp, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            # 提取并显示ROI区域
            roi_region = clone[y:y+h, x:x+w]
            if roi_region.size > 0:
                cv2.imshow('ROI Region', roi_region)

        # 显示正在绘制的矩形
        if drawing and fx != -1 and fy != -1:
            cv2.rectangle(temp, (ix, iy), (fx, fy), (0, 255, 0), 2)

        cv2.imshow('Select ROI', temp)
        key = cv2.waitKey(1) & 0xFF

        if key == ord('r'):  # 重置ROI
            roi = None
            cv2.destroyWindow('ROI Region') if cv2.getWindowProperty('ROI Region', cv2.WND_PROP_VISIBLE) >= 0 else None
        elif key == ord('s') and roi is not None:  # 保存ROI区域
            x, y, w, h = roi
            roi_region = clone[y:y+h, x:x+w]
            cv2.imwrite('camera_roi.jpg', roi_region)
            print(f"摄像头ROI区域已保存为 camera_roi.jpg，坐标: ({x}, {y}, {w}, {h})")
        elif key == ord('q'):  # 退出
            break

    print("正在关闭摄像头...")
    cap.release()
    cv2.destroyAllWindows()
    print("程序已退出")
# 主函数，调用process_camera()函数开始处理摄像头输入
if __name__ == "__main__":
    process_camera()