import cv2
import numpy as np

# 全局变量
roi = None
drawing = False
ix, iy = -1, -1
fx, fy = -1, -1


def draw_rectangle(event, x, y, flags, param):
    global ix, iy, fx, fy, drawing, roi

    if event == cv2.EVENT_LBUTTONDOWN:
        drawing = True
        ix, iy = x, y

    elif event == cv2.EVENT_MOUSEMOVE:
        if drawing:
            fx, fy = x, y

    elif event == cv2.EVENT_LBUTTONUP:
        drawing = False
        fx, fy = x, y
        roi = (min(ix, fx), min(iy, fy), abs(fx - ix), abs(fy - iy))


def process_camera():
    global roi
    cap = cv2.VideoCapture(0)
    cv2.namedWindow('Select ROI')
    cv2.setMouseCallback('Select ROI', draw_rectangle)

    while True:
        ret, frame = cap.read()
        if not ret:
            break
        clone = frame.copy()
        temp = frame.copy()

        if roi is not None:
            x, y, w, h = roi
            cv2.rectangle(temp, (x, y), (x + w, y + h), (0, 255, 0), 2)
            text = f"ROI: ({x}, {y}, {w}, {h})"
            cv2.putText(temp, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        if drawing and fx != -1 and fy != -1:
            cv2.rectangle(temp, (ix, iy), (fx, fy), (0, 255, 0), 2)

        cv2.imshow('Select ROI', temp)
        key = cv2.waitKey(1) & 0xFF
        if key == ord('r'):
            frame = clone.copy()
            roi = None
        elif key == ord('q'):
            break

    cap.release()
    cv2.destroyAllWindows()


if __name__ == "__main__":
    process_camera()
    