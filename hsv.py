import cv2
import numpy as np

def nothing(val):
    """滑动条回调函数"""
    pass

def process_hsv_image():
    """处理图像的HSV颜色提取"""
    # 读取图像
    image = cv2.imread('yqmr.jpg')
    if image is None:
        print("错误：无法读取 yqmr.jpg 文件")
        print("请确保 yqmr.jpg 文件存在于当前目录")
        return

    print("图像加载成功！")
    print(f"图像尺寸: {image.shape[1]}x{image.shape[0]}")
    print("\n操作说明:")
    print("- 使用滑动条调整HSV颜色范围")
    print("- 按 's' 键保存当前结果")
    print("- 按 'r' 键重置为默认值")
    print("- 按 'q' 键退出程序")

    # 转换为HSV色彩空间
    hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

    # 创建控制窗口
    cv2.namedWindow('HSV Controls')
    cv2.resizeWindow('HSV Controls', 400, 300)

    # 创建HSV范围滑动条
    cv2.createTrackbar('H Min', 'HSV Controls', 0, 179, nothing)
    cv2.createTrackbar('S Min', 'HSV Controls', 100, 255, nothing)
    cv2.createTrackbar('V Min', 'HSV Controls', 100, 255, nothing)
    cv2.createTrackbar('H Max', 'HSV Controls', 10, 179, nothing)
    cv2.createTrackbar('S Max', 'HSV Controls', 255, 255, nothing)
    cv2.createTrackbar('V Max', 'HSV Controls', 255, 255, nothing)

    # 预设颜色选择
    color_presets = {
        'red': ([0, 100, 100], [10, 255, 255]),
        'green': ([40, 50, 50], [80, 255, 255]),
        'blue': ([100, 50, 50], [130, 255, 255]),
        'yellow': ([20, 100, 100], [30, 255, 255]),
        'orange': ([10, 100, 100], [20, 255, 255]),
        'purple': ([130, 50, 50], [160, 255, 255])
    }

    current_preset = 'red'
    print(f"\n当前预设: {current_preset}")
    print("按数字键切换预设颜色:")
    print("1-红色 2-绿色 3-蓝色 4-黄色 5-橙色 6-紫色")

    while True:
        # 获取滑动条当前值
        h_min = cv2.getTrackbarPos('H Min', 'HSV Controls')
        s_min = cv2.getTrackbarPos('S Min', 'HSV Controls')
        v_min = cv2.getTrackbarPos('V Min', 'HSV Controls')
        h_max = cv2.getTrackbarPos('H Max', 'HSV Controls')
        s_max = cv2.getTrackbarPos('S Max', 'HSV Controls')
        v_max = cv2.getTrackbarPos('V Max', 'HSV Controls')

        # 定义HSV范围
        lower_hsv = np.array([h_min, s_min, v_min])
        upper_hsv = np.array([h_max, s_max, v_max])

        # 创建掩码
        mask = cv2.inRange(hsv_image, lower_hsv, upper_hsv)

        # 形态学操作去除噪声
        kernel = np.ones((3, 3), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)

        # 应用掩码
        result = cv2.bitwise_and(image, image, mask=mask)

        # 在原图上显示HSV值信息
        info_image = image.copy()
        cv2.putText(info_image, f"HSV: [{h_min},{s_min},{v_min}] - [{h_max},{s_max},{v_max}]",
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        cv2.putText(info_image, f"Preset: {current_preset}",
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

        # 显示图像
        cv2.imshow('Original Image', info_image)
        cv2.imshow('HSV Mask', mask)
        cv2.imshow('Extracted Color', result)

        key = cv2.waitKey(1) & 0xFF

        # 预设颜色切换
        if key == ord('1'):  # 红色
            current_preset = 'red'
            lower, upper = color_presets[current_preset]
            cv2.setTrackbarPos('H Min', 'HSV Controls', lower[0])
            cv2.setTrackbarPos('S Min', 'HSV Controls', lower[1])
            cv2.setTrackbarPos('V Min', 'HSV Controls', lower[2])
            cv2.setTrackbarPos('H Max', 'HSV Controls', upper[0])
            cv2.setTrackbarPos('S Max', 'HSV Controls', upper[1])
            cv2.setTrackbarPos('V Max', 'HSV Controls', upper[2])
            print(f"切换到预设: {current_preset}")

        elif key == ord('2'):  # 绿色
            current_preset = 'green'
            lower, upper = color_presets[current_preset]
            cv2.setTrackbarPos('H Min', 'HSV Controls', lower[0])
            cv2.setTrackbarPos('S Min', 'HSV Controls', lower[1])
            cv2.setTrackbarPos('V Min', 'HSV Controls', lower[2])
            cv2.setTrackbarPos('H Max', 'HSV Controls', upper[0])
            cv2.setTrackbarPos('S Max', 'HSV Controls', upper[1])
            cv2.setTrackbarPos('V Max', 'HSV Controls', upper[2])
            print(f"切换到预设: {current_preset}")

        elif key == ord('3'):  # 蓝色
            current_preset = 'blue'
            lower, upper = color_presets[current_preset]
            cv2.setTrackbarPos('H Min', 'HSV Controls', lower[0])
            cv2.setTrackbarPos('S Min', 'HSV Controls', lower[1])
            cv2.setTrackbarPos('V Min', 'HSV Controls', lower[2])
            cv2.setTrackbarPos('H Max', 'HSV Controls', upper[0])
            cv2.setTrackbarPos('S Max', 'HSV Controls', upper[1])
            cv2.setTrackbarPos('V Max', 'HSV Controls', upper[2])
            print(f"切换到预设: {current_preset}")

        elif key == ord('4'):  # 黄色
            current_preset = 'yellow'
            lower, upper = color_presets[current_preset]
            cv2.setTrackbarPos('H Min', 'HSV Controls', lower[0])
            cv2.setTrackbarPos('S Min', 'HSV Controls', lower[1])
            cv2.setTrackbarPos('V Min', 'HSV Controls', lower[2])
            cv2.setTrackbarPos('H Max', 'HSV Controls', upper[0])
            cv2.setTrackbarPos('S Max', 'HSV Controls', upper[1])
            cv2.setTrackbarPos('V Max', 'HSV Controls', upper[2])
            print(f"切换到预设: {current_preset}")

        elif key == ord('5'):  # 橙色
            current_preset = 'orange'
            lower, upper = color_presets[current_preset]
            cv2.setTrackbarPos('H Min', 'HSV Controls', lower[0])
            cv2.setTrackbarPos('S Min', 'HSV Controls', lower[1])
            cv2.setTrackbarPos('V Min', 'HSV Controls', lower[2])
            cv2.setTrackbarPos('H Max', 'HSV Controls', upper[0])
            cv2.setTrackbarPos('S Max', 'HSV Controls', upper[1])
            cv2.setTrackbarPos('V Max', 'HSV Controls', upper[2])
            print(f"切换到预设: {current_preset}")

        elif key == ord('6'):  # 紫色
            current_preset = 'purple'
            lower, upper = color_presets[current_preset]
            cv2.setTrackbarPos('H Min', 'HSV Controls', lower[0])
            cv2.setTrackbarPos('S Min', 'HSV Controls', lower[1])
            cv2.setTrackbarPos('V Min', 'HSV Controls', lower[2])
            cv2.setTrackbarPos('H Max', 'HSV Controls', upper[0])
            cv2.setTrackbarPos('S Max', 'HSV Controls', upper[1])
            cv2.setTrackbarPos('V Max', 'HSV Controls', upper[2])
            print(f"切换到预设: {current_preset}")

        elif key == ord('s'):  # 保存结果
            cv2.imwrite('hsv_mask.jpg', mask)
            cv2.imwrite('hsv_result.jpg', result)
            print(f"已保存: hsv_mask.jpg, hsv_result.jpg")
            print(f"当前HSV范围: [{h_min},{s_min},{v_min}] - [{h_max},{s_max},{v_max}]")

        elif key == ord('r'):  # 重置为红色预设
            current_preset = 'red'
            lower, upper = color_presets[current_preset]
            cv2.setTrackbarPos('H Min', 'HSV Controls', lower[0])
            cv2.setTrackbarPos('S Min', 'HSV Controls', lower[1])
            cv2.setTrackbarPos('V Min', 'HSV Controls', lower[2])
            cv2.setTrackbarPos('H Max', 'HSV Controls', upper[0])
            cv2.setTrackbarPos('S Max', 'HSV Controls', upper[1])
            cv2.setTrackbarPos('V Max', 'HSV Controls', upper[2])
            print("已重置为红色预设")

        elif key == ord('q'):  # 退出
            break

    cv2.destroyAllWindows()
    print("程序已退出")

if __name__ == "__main__":
    process_hsv_image()