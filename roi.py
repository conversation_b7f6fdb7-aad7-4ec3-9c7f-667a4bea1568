import cv2
import numpy as np

# 全局变量
roi = None
drawing = False
ix, iy = -1, -1
fx, fy = -1, -1


def draw_rectangle(event, x, y, flags, param):
    global ix, iy, fx, fy, drawing, roi

    if event == cv2.EVENT_LBUTTONDOWN:
        drawing = True
        ix, iy = x, y

    elif event == cv2.EVENT_MOUSEMOVE:
        if drawing:
            fx, fy = x, y

    elif event == cv2.EVENT_LBUTTONUP:
        drawing = False
        fx, fy = x, y
        roi = (min(ix, fx), min(iy, fy), abs(fx - ix), abs(fy - iy))


def process_image():
    global roi
    # 读取图片
    image = cv2.imread('image.jpg')
    if image is None:
        print("错误：无法读取 image.jpg 文件")
        return

    clone = image.copy()  # 保存原始图片
    cv2.namedWindow('Select ROI')
    cv2.setMouseCallback('Select ROI', draw_rectangle)

    while True:
        temp = clone.copy()  # 从原始图片复制

        # 显示已选择的ROI区域
        if roi is not None:
            x, y, w, h = roi
            cv2.rectangle(temp, (x, y), (x + w, y + h), (0, 255, 0), 2)
            text = f"ROI: ({x}, {y}, {w}, {h})"
            cv2.putText(temp, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            # 提取并显示ROI区域
            roi_region = clone[y:y+h, x:x+w]
            if roi_region.size > 0:
                cv2.imshow('ROI Region', roi_region)

        # 显示正在绘制的矩形
        if drawing and fx != -1 and fy != -1:
            cv2.rectangle(temp, (ix, iy), (fx, fy), (0, 255, 0), 2)

        cv2.imshow('Select ROI', temp)
        key = cv2.waitKey(1) & 0xFF

        if key == ord('r'):  # 重置ROI
            roi = None
            cv2.destroyWindow('ROI Region') if 'ROI Region' in [cv2.getWindowProperty(w, cv2.WND_PROP_VISIBLE) for w in ['ROI Region']] else None
        elif key == ord('s') and roi is not None:  # 保存ROI区域
            x, y, w, h = roi
            roi_region = clone[y:y+h, x:x+w]
            cv2.imwrite('roi_extracted.jpg', roi_region)
            print(f"ROI区域已保存为 roi_extracted.jpg，坐标: ({x}, {y}, {w}, {h})")
        elif key == ord('q'):  # 退出
            break

    cv2.destroyAllWindows()


if __name__ == "__main__":
    process_image()