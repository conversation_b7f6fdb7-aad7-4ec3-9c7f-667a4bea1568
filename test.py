import cv2
import numpy as np

# 打开摄像头
cap = cv2.VideoCapture(0)

# 获取帧的宽度和高度
ret, frame = cap.read()
if ret:
    height, width, _ = frame.shape
    # 定义中心 ROI 区域的大小
    roi_size = 100
    # 计算中心 ROI 区域的坐标
    x = (width - roi_size) // 2
    y = (height - roi_size) // 2
    roi = (x, y, roi_size, roi_size)
    learning = False
    learn_frames = 0
    lower_hsv_list = []
    upper_hsv_list = []
    lower_hsv = None
    upper_hsv = None

while True:
    ret, frame = cap.read()
    if not ret:
        break

    # 绘制中心 ROI 区域
    x, y, w, h = roi
    cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)

    if learning:
        if learn_frames < 30:
            # 提取 ROI 区域
            roi_frame = frame[y:y + h, x:x + w]
            # 转换为 HSV 颜色空间
            hsv_roi = cv2.cvtColor(roi_frame, cv2.COLOR_BGR2HSV)

            # 统计 HSV 通道 1% 到 99% 的百分位
            h_percentiles = np.percentile(hsv_roi[:, :, 0], [1, 99])
            s_percentiles = np.percentile(hsv_roi[:, :, 1], [1, 99])
            v_percentiles = np.percentile(hsv_roi[:, :, 2], [1, 99])

            lower_hsv_list.append(np.array([h_percentiles[0], s_percentiles[0], v_percentiles[0]]))
            upper_hsv_list.append(np.array([h_percentiles[1], s_percentiles[1], v_percentiles[1]]))

            learn_frames += 1
        else:
            # 学习结束，计算最终的阈值
            lower_hsv = np.min(lower_hsv_list, axis=0)
            upper_hsv = np.max(upper_hsv_list, axis=0)
            learning = False

    if lower_hsv is not None and upper_hsv is not None:
        # 将整个帧转换为 HSV 颜色空间
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        # 创建掩码
        mask = cv2.inRange(hsv, lower_hsv, upper_hsv)

        # 对掩码进行形态学操作，去除噪声
        kernel = np.ones((5, 5), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)

        # 查找轮廓
        contours, _ = cv2.findContours(mask.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 绘制轮廓并标注中心点
        if len(contours) > 0:
            for contour in contours:
                # 计算轮廓的面积
                area = cv2.contourArea(contour)
                if area > 100:  # 过滤掉小的轮廓
                    # 绘制轮廓
                    cv2.drawContours(frame, [contour], -1, (0, 255, 0), 2)

                    # 计算轮廓的矩
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cX = int(M["m10"] / M["m00"])
                        cY = int(M["m01"] / M["m00"])
                        # 标注中心点
                        cv2.circle(frame, (cX, cY), 5, (255, 0, 0), -1)
                        cv2.putText(frame, f"({cX}, {cY})", (cX - 20, cY - 20),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)

        cv2.imshow('Mask', mask)

    cv2.imshow('Frame', frame)

    key = cv2.waitKey(1) & 0xFF
    if key == ord('g'):
        learning = True
        learn_frames = 0
        lower_hsv_list = []
        upper_hsv_list = []
        lower_hsv = None
        upper_hsv = None
    elif key == ord('q'):
        break

# 释放摄像头并关闭所有窗口
cap.release()
cv2.destroyAllWindows()